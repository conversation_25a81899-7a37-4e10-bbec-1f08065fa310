---
title: "Assignment 1: Exploring Word Vectors"
format: 
  html:
    toc: true
    toc-title: Contents
    toc-depth: 4
    self-contained: true
    number-sections: false
jupyter: python3
---

# All Import Statements Defined Here
# Note: Do not add to this list.
# ----------------

import sys

from gensim.models import KeyedVectors
from gensim.test.utils import datapath
import pprint
import matplotlib.pyplot as plt
plt.rcParams['figure.figsize'] = [10, 5]

# load_dataset comes from huggingface's datasets library
from datasets import load_dataset

# Load the IMDB dataset uploaded by stanfordnlp from Hugging Face
imdb_dataset = load_dataset("stanfordnlp/imdb", name="plain_text")

import re
import numpy as np
import random
import scipy as sp
from sklearn.decomposition import TruncatedSVD
from sklearn.decomposition import PCA

START_TOKEN = '<START>'
END_TOKEN = '<END>'
NUM_SAMPLES = 150

np.random.seed(0)
random.seed(0)
# ----------------

def read_corpus():
    """ Read files from the Large Movie Review Dataset.
        Params:
            category (string): category name
        Return:
            list of lists, with words from each of the processed files
    """
    files = imdb_dataset["train"]["text"][:NUM_SAMPLES]
    return [[START_TOKEN] + [re.sub(r'[^\w]', '', w.lower()) for w in f.split(" ")] + [END_TOKEN] for f in files]

imdb_corpus = read_corpus()
pprint.pprint(imdb_corpus[:3], compact=True, width=100)
print("corpus size: ", len(imdb_corpus[0]))

def distinct_words(corpus):
    """ Determine a list of distinct words for the corpus.
        Params:
            corpus (list of list of strings): corpus of documents
        Return:
            corpus_words (list of strings): sorted list of distinct words across the corpus
            n_corpus_words (integer): number of distinct words across the corpus
    """
    corpus_words = []
    n_corpus_words = -1
    
    # ------------------
    # Write your implementation here.
    
    # Step 1: Flatten the nested list structure using list comprehension
    # This takes each document in corpus, then each word in that document
    # and creates a single flat list of all words
    all_words = [word for document in corpus for word in document]
    
    # Step 2: Remove duplicates by converting to set, then back to list
    # Sets automatically handle uniqueness for us
    unique_words = list(set(all_words))
    
    # Step 3: Sort the words alphabetically for consistent ordering
    # This ensures our word indices will be deterministic
    corpus_words = sorted(unique_words)
    
    # Step 4: Count the number of distinct words
    n_corpus_words = len(corpus_words)
    
    # ------------------

    return corpus_words, n_corpus_words

# ---------------------
# Run this sanity check
# Note that this not an exhaustive check for correctness.
# ---------------------

# Define toy corpus
test_corpus = ["{} All that glitters isn't gold {}".format(START_TOKEN, END_TOKEN).split(" "), "{} All's well that ends well {}".format(START_TOKEN, END_TOKEN).split(" ")]
test_corpus_words, num_corpus_words = distinct_words(test_corpus)

# Correct answers
ans_test_corpus_words = sorted([START_TOKEN, "All", "ends", "that", "gold", "All's", "glitters", "isn't", "well", END_TOKEN])
ans_num_corpus_words = len(ans_test_corpus_words)

# Test correct number of words
assert(num_corpus_words == ans_num_corpus_words), "Incorrect number of distinct words. Correct: {}. Yours: {}".format(ans_num_corpus_words, num_corpus_words)

# Test correct words
assert (test_corpus_words == ans_test_corpus_words), "Incorrect corpus_words.\nCorrect: {}\nYours:   {}".format(str(ans_test_corpus_words), str(test_corpus_words))

# Print Success
print ("-" * 80)
print("Passed All Tests!")
print ("-" * 80)

def compute_co_occurrence_matrix(corpus, window_size=4):
    """ Compute co-occurrence matrix for the given corpus and window_size (default of 4).
    
        Note: Each word in a document should be at the center of a window. Words near edges will have a smaller
              number of co-occurring words.
              
              For example, if we take the document "<START> All that glitters is not gold <END>" with window size of 4,
              "All" will co-occur with "<START>", "that", "glitters", "is", and "not".
    
        Params:
            corpus (list of list of strings): corpus of documents
            window_size (int): size of context window
        Return:
            M (a symmetric numpy matrix of shape (number of unique words in the corpus , number of unique words in the corpus)): 
                Co-occurence matrix of word counts. 
                The ordering of the words in the rows/columns should be the same as the ordering of the words given by the distinct_words function.
            word2ind (dict): dictionary that maps word to index (i.e. row/column number) for matrix M.
    """
    words, n_words = distinct_words(corpus)
    M = None
    word2ind = {}
    
    # ------------------
    # Write your implementation here.
    
    # Step 1: Create the word-to-index mapping
    # This dictionary will let us quickly convert words to matrix positions
    word2ind = {word: i for i, word in enumerate(words)}
    
    # Step 2: Initialize our co-occurrence matrix with zeros
    # This will be a square matrix of size (vocab_size x vocab_size)
    M = np.zeros((n_words, n_words))
    
    # Step 3: For each document in our corpus, count co-occurrences
    for document in corpus:
        # Step 4: For each word position in the document
        for center_idx, center_word in enumerate(document):
            # Get the matrix index for our center word
            center_word_idx = word2ind[center_word]
            
            # Step 5: Look at words within the window around our center word
            # Window goes from (center_idx - window_size) to (center_idx + window_size)
            start_idx = max(0, center_idx - window_size)  # Don't go below 0
            end_idx = min(len(document), center_idx + window_size + 1)  # Don't exceed document length
            
            # Step 6: Count co-occurrences with each word in the window
            for context_idx in range(start_idx, end_idx):
                # Skip the center word itself (a word doesn't co-occur with itself)
                if context_idx == center_idx:
                    continue
                    
                context_word = document[context_idx]
                context_word_idx = word2ind[context_word]
                
                # Increment the co-occurrence count
                # We add to both M[center][context] and M[context][center] to ensure symmetry
                M[center_word_idx, context_word_idx] += 1
    
    # ------------------

    return M, word2ind

# ---------------------
# Run this sanity check
# Note that this is not an exhaustive check for correctness.
# ---------------------

# Define toy corpus and get student's co-occurrence matrix
test_corpus = ["{} All that glitters isn't gold {}".format(START_TOKEN, END_TOKEN).split(" "), "{} All's well that ends well {}".format(START_TOKEN, END_TOKEN).split(" ")]
M_test, word2ind_test = compute_co_occurrence_matrix(test_corpus, window_size=1)

# Correct M and word2ind
M_test_ans = np.array( 
    [[0., 0., 0., 0., 0., 0., 1., 0., 0., 1.,],
     [0., 0., 1., 1., 0., 0., 0., 0., 0., 0.,],
     [0., 1., 0., 0., 0., 0., 0., 0., 1., 0.,],
     [0., 1., 0., 0., 0., 0., 0., 0., 0., 1.,],
     [0., 0., 0., 0., 0., 0., 0., 0., 1., 1.,],
     [0., 0., 0., 0., 0., 0., 0., 1., 1., 0.,],
     [1., 0., 0., 0., 0., 0., 0., 1., 0., 0.,],
     [0., 0., 0., 0., 0., 1., 1., 0., 0., 0.,],
     [0., 0., 1., 0., 1., 1., 0., 0., 0., 1.,],
     [1., 0., 0., 1., 1., 0., 0., 0., 1., 0.,]]
)
ans_test_corpus_words = sorted([START_TOKEN, "All", "ends", "that", "gold", "All's", "glitters", "isn't", "well", END_TOKEN])
word2ind_ans = dict(zip(ans_test_corpus_words, range(len(ans_test_corpus_words))))

# Test correct word2ind
assert (word2ind_ans == word2ind_test), "Your word2ind is incorrect:\nCorrect: {}\nYours: {}".format(word2ind_ans, word2ind_test)

# Test correct M shape
assert (M_test.shape == M_test_ans.shape), "M matrix has incorrect shape.\nCorrect: {}\nYours: {}".format(M_test.shape, M_test_ans.shape)

# Test correct M values
for w1 in word2ind_ans.keys():
    idx1 = word2ind_ans[w1]
    for w2 in word2ind_ans.keys():
        idx2 = word2ind_ans[w2]
        student = M_test[idx1, idx2]
        correct = M_test_ans[idx1, idx2]
        if student != correct:
            print("Correct M:")
            print(M_test_ans)
            print("Your M: ")
            print(M_test)
            raise AssertionError("Incorrect count at index ({}, {})=({}, {}) in matrix M. Yours has {} but should have {}.".format(idx1, idx2, w1, w2, student, correct))

# Print Success
print ("-" * 80)
print("Passed All Tests!")
print ("-" * 80)

def reduce_to_k_dim(M, k=2):
    """ Reduce a co-occurence count matrix of dimensionality (num_corpus_words, num_corpus_words)
        to a matrix of dimensionality (num_corpus_words, k) using the following SVD function from Scikit-Learn:
            - http://scikit-learn.org/stable/modules/generated/sklearn.decomposition.TruncatedSVD.html
    
        Params:
            M (numpy matrix of shape (number of unique words in the corpus , number of unique words in the corpus)): co-occurence matrix of word counts
            k (int): embedding size of each word after dimension reduction
        Return:
            M_reduced (numpy matrix of shape (number of corpus words, k)): matrix of k-dimensioal word embeddings.
                    In terms of the SVD from math class, this actually returns U * S
    """    
    n_iters = 10    # Use this parameter in your call to `TruncatedSVD`
    M_reduced = None
    print("Running Truncated SVD over %i words..." % (M.shape[0]))
    
    # ------------------
    # Write your implementation here.
    
    # Step 1: Create the TruncatedSVD object
    # We specify k components (dimensions we want to keep)
    # n_iter controls how many iterations the algorithm runs for better accuracy
    # random_state makes our results reproducible
    svd = TruncatedSVD(n_components=k, n_iter=n_iters, random_state=42)
    
    # Step 2: Fit the SVD to our co-occurrence matrix and transform it
    # This finds the k most important patterns in our word relationships
    # and projects each word onto these k dimensions
    M_reduced = svd.fit_transform(M)
    
    # What's happening mathematically:
    # SVD decomposes M into U * Σ * V^T
    # TruncatedSVD.fit_transform() returns U * Σ (the first k columns)
    # This gives us word embeddings where each word is represented by k numbers
    # These k numbers capture the most important patterns of word co-occurrence
    
    # ------------------

    print("Done.")
    return M_reduced

# ---------------------
# Run this sanity check
# Note that this is not an exhaustive check for correctness 
# In fact we only check that your M_reduced has the right dimensions.
# ---------------------

# Define toy corpus and run student code
test_corpus = ["{} All that glitters isn't gold {}".format(START_TOKEN, END_TOKEN).split(" "), "{} All's well that ends well {}".format(START_TOKEN, END_TOKEN).split(" ")]
M_test, word2ind_test = compute_co_occurrence_matrix(test_corpus, window_size=1)
M_test_reduced = reduce_to_k_dim(M_test, k=2)

# Test proper dimensions
assert (M_test_reduced.shape[0] == 10), "M_reduced has {} rows; should have {}".format(M_test_reduced.shape[0], 10)
assert (M_test_reduced.shape[1] == 2), "M_reduced has {} columns; should have {}".format(M_test_reduced.shape[1], 2)

# Print Success
print ("-" * 80)
print("Passed All Tests!")
print ("-" * 80)

def plot_embeddings(M_reduced, word2ind, words):
    """ Plot in a scatterplot the embeddings of the words specified in the list "words".
        NOTE: do not plot all the words listed in M_reduced / word2ind.
        Include a label next to each point.
        
        Params:
            M_reduced (numpy matrix of shape (number of unique words in the corpus , 2)): matrix of 2-dimensioal word embeddings
            word2ind (dict): dictionary that maps word to indices for matrix M
            words (list of strings): words whose embeddings we want to visualize
    """

    # ------------------
    # Write your implementation here.
    
    # Step 1: Extract the coordinates for each word we want to plot
    # We need to look up each word's index and get its 2D coordinates
    x_coords = []
    y_coords = []
    
    for word in words:
        # Get the index of this word in our matrix
        word_idx = word2ind[word]
        
        # Extract the x and y coordinates for this word
        x_coords.append(M_reduced[word_idx, 0])  # First dimension
        y_coords.append(M_reduced[word_idx, 1])  # Second dimension
    
    # Step 2: Create the scatter plot
    plt.figure(figsize=(10, 8))  # Make the plot reasonably large
    plt.scatter(x_coords, y_coords, alpha=0.7, s=60)  # s controls point size, alpha controls transparency
    
    # Step 3: Add word labels next to each point
    # This is what makes the plot interpretable - we can see which word is which
    for i, word in enumerate(words):
        plt.annotate(word, 
                    (x_coords[i], y_coords[i]),  # Position of the word
                    xytext=(5, 5),  # Offset the text slightly from the point
                    textcoords='offset points',  # Use offset in points
                    fontsize=12,
                    ha='left')  # Horizontal alignment
    
    # Step 4: Add labels and title to make the plot clear
    plt.xlabel('First Principal Component', fontsize=12)
    plt.ylabel('Second Principal Component', fontsize=12)
    plt.title('2D Word Embeddings from Co-occurrence Matrix', fontsize=14)
    plt.grid(True, alpha=0.3)  # Add a light grid for easier reading
    
    # Step 5: Show the plot
    plt.tight_layout()  # Adjust spacing to prevent label cutoff
    plt.show()
    
    # ------------------

# ---------------------
# Run this sanity check
# Note that this is not an exhaustive check for correctness.
# The plot produced should look like the included file question_1.4_test.png 
# ---------------------

print ("-" * 80)
print ("Outputted Plot:")

M_reduced_plot_test = np.array([[1, 1], [-1, -1], [1, -1], [-1, 1], [0, 0]])
word2ind_plot_test = {'test1': 0, 'test2': 1, 'test3': 2, 'test4': 3, 'test5': 4}
words = ['test1', 'test2', 'test3', 'test4', 'test5']
plot_embeddings(M_reduced_plot_test, word2ind_plot_test, words)

print ("-" * 80)

# -----------------------------
# Run This Cell to Produce Your Plot
# ------------------------------
imdb_corpus = read_corpus()
M_co_occurrence, word2ind_co_occurrence = compute_co_occurrence_matrix(imdb_corpus)
M_reduced_co_occurrence = reduce_to_k_dim(M_co_occurrence, k=2)

# Rescale (normalize) the rows to make them each of unit-length
M_lengths = np.linalg.norm(M_reduced_co_occurrence, axis=1)
M_normalized = M_reduced_co_occurrence / M_lengths[:, np.newaxis] # broadcasting

words = ['movie', 'book', 'mysterious', 'story', 'fascinating', 'good', 'interesting', 'large', 'massive', 'huge']

plot_embeddings(M_normalized, word2ind_co_occurrence, words)

def load_embedding_model():
    """ Load GloVe Vectors
        Return:
            wv_from_bin: All 400000 embeddings, each length 200
    """
    import gensim.downloader as api
    wv_from_bin = api.load("glove-wiki-gigaword-200")
    print("Loaded vocab size %i" % len(list(wv_from_bin.index_to_key)))
    return wv_from_bin
wv_from_bin = load_embedding_model()

def get_matrix_of_vectors(wv_from_bin, required_words):
    """ Put the GloVe vectors into a matrix M.
        Param:
            wv_from_bin: KeyedVectors object; the 400000 GloVe vectors loaded from file
        Return:
            M: numpy matrix shape (num words, 200) containing the vectors
            word2ind: dictionary mapping each word to its row number in M
    """
    import random
    words = list(wv_from_bin.index_to_key)
    print("Shuffling words ...")
    random.seed(225)
    random.shuffle(words)
    print("Putting %i words into word2ind and matrix M..." % len(words))
    word2ind = {}
    M = []
    curInd = 0
    for w in words:
        try:
            M.append(wv_from_bin.get_vector(w))
            word2ind[w] = curInd
            curInd += 1
        except KeyError:
            continue
    for w in required_words:
        if w in words:
            continue
        try:
            M.append(wv_from_bin.get_vector(w))
            word2ind[w] = curInd
            curInd += 1
        except KeyError:
            continue
    M = np.stack(M)
    print("Done.")
    return M, word2ind

# -----------------------------------------------------------------
# Run Cell to Reduce 200-Dimensional Word Embeddings to k Dimensions
# Note: This should be quick to run
# -----------------------------------------------------------------
M, word2ind = get_matrix_of_vectors(wv_from_bin, words)
M_reduced = reduce_to_k_dim(M, k=2)

# Rescale (normalize) the rows to make them each of unit-length
M_lengths = np.linalg.norm(M_reduced, axis=1)
M_reduced_normalized = M_reduced / M_lengths[:, np.newaxis] # broadcasting

words = ['movie', 'book', 'mysterious', 'story', 'fascinating', 'good', 'interesting', 'large', 'massive', 'huge']

plot_embeddings(M_reduced_normalized, word2ind, words)

# ------------------
# Write your implementation here.

candidate_words = [
    "bank",      
    "bow",       
    "bark",     
    "bat",      
    "spring",    
    "leaves",    
    "scoop",    
    "duck",    
    "bear",     
    "rose",      
    "date",      
    "club",      
    "coach",  
    "court",    
    "fine",      
]

#testing now
for word in candidate_words:
    try:
        print(f"\nTesting word: '{word}'")
        print("-" * 30)
        
        # Get the top 10 most similar words
        similar_words = wv_from_bin.most_similar(word, topn=10)
        
        print("Top 10 most similar words:")
        for i, (similar_word, similarity) in enumerate(similar_words, 1):
            print(f"{i:2d}. {similar_word:15s} (similarity: {similarity:.3f})")
        
        print()  # Add some space between words
        
    except KeyError:
        print(f"Word '{word}' not found in vocabulary")
        continue

print("\nAnalysis Questions to Consider:")
print("1. Which word shows multiple meanings in its top-10?")
print("2. Can you identify the different semantic clusters?")
print("3. Why might some polysemous words only show one meaning?")

print("\n" + "=" * 60)
print("DEEPER ANALYSIS OF A PROMISING EXAMPLE")
print("=" * 60)

# Based on common results, show multiple meanings of 'spring'
try:
    target_word = "spring"
    print(f"Detailed analysis of '{target_word}':")
    
    similar_words = wv_from_bin.most_similar(target_word, topn=15)  
    
    print(f"\nTop 15 words most similar to '{target_word}':")
    for i, (word, sim) in enumerate(similar_words, 1):
        print(f"{i:2d}. {word:15s} (similarity: {sim:.3f})")
    
    print(f"\nSemantic analysis of '{target_word}' meanings:")
    print("Season meaning: Look for words like 'summer', 'autumn', 'winter'")
    print("Mechanical meaning: Look for words like 'coil', 'elastic', 'tension'") 
    print("Water meaning: Look for words like 'fountain', 'well', 'water'")
    print("Action meaning: Look for words like 'jump', 'leap', 'bounce'")
    
except KeyError:
    print(f"'{target_word}' not found in vocabulary")


# ------------------
# Write your implementation here.
import gensim.downloader as api


def analyze_cosine_distance_anomalies(model):
    """
    Identifies cases where antonyms have a smaller cosine distance (i.e., are "closer" 
    in vector space) than synonyms to a base word.
    
    This analysis demonstrates that vector embeddings capture contextual similarity,
    not just semantic opposition or equivalence. Antonyms frequently appear in the 
    same linguistic contexts as their counterparts.
    
    Args:
        model: A loaded Gensim Word2Vec or GloVe model object.
    """
    
    # Define the set of word triplets for analysis.
    semantic_test_set = [
        ("happy", "joyful", "sad"),
        ("happy", "cheerful", "sad"),
        ("happy", "elated", "miserable"),
        ("big", "large", "small"),
        ("big", "huge", "tiny"),
        ("hot", "warm", "cold"),
        ("hot", "sweltering", "cold"),
        ("fast", "quick", "slow"),
        ("fast", "rapid", "slow"),
        ("good", "excellent", "bad"),
        ("good", "wonderful", "terrible"),
        ("bright", "brilliant", "dark"),
        ("bright", "luminous", "dim"),
        ("rich", "wealthy", "poor"),
        ("rich", "affluent", "poor"),
        ("strong", "powerful", "weak"),
        ("strong", "mighty", "weak"),
    ]
    
    identified_anomalies = []
    
    print("Analyzing for Cosine Distance Anomalies...")
    print("-" * 50)
    
    for base_word, synonym_word, antonym_word in semantic_test_set:
        try:
            # Calculate cosine distance. Note: gensim's .distance() is 1 - cosine_similarity.
            # A smaller distance value means higher similarity (closer vectors).
            distance_to_synonym = model.distance(base_word, synonym_word)
            distance_to_antonym = model.distance(base_word, antonym_word)
            
            print(f"\nTesting: '{base_word}' vs '{synonym_word}' (Synonym) vs '{antonym_word}' (Antonym)")
            print(f"Distance to synonym '{synonym_word}': {distance_to_synonym:.4f}")
            print(f"Distance to antonym '{antonym_word}': {distance_to_antonym:.4f}")
            
            # Check for the anomaly: is the antonym vectorially closer than the synonym?
            if distance_to_antonym < distance_to_synonym:
                anomaly_data = (base_word, synonym_word, antonym_word, distance_to_synonym, distance_to_antonym)
                identified_anomalies.append(anomaly_data)
                print(f"*** ANOMALY DETECTED! '{antonym_word}' is closer to '{base_word}' than '{synonym_word}' is.")
                
        except KeyError as e:
            # This handles cases where a word in our test set is not in the model's vocabulary.
            print(f"Word not found in model vocabulary: {e}")
            continue
            
    return identified_anomalies


# Load a pre-trained model 
print("Loading GloVe model... (This might take a moment)")
try:
    glove_model = api.load("glove-wiki-gigaword-300")
    print("Model loaded successfully.")

    anomaly_results = analyze_cosine_distance_anomalies(glove_model)

    print(f"\n{'='*60}")
    print("FINAL ANALYSIS OF DISCOVERED ANOMALIES")
    print(f"{'='*60}")

    if anomaly_results:
    
        base, synonym, antonym, syn_dist, ant_dist = anomaly_results[0]
        
        print(f"\nSelected Example for Explanation:")
        print(f"Base Word: '{base}'")
        print(f"Synonym:   '{synonym}'")
        print(f"Antonym:   '{antonym}'")
        print(f"\nResult: Cosine_Distance('{base}', '{antonym}') = {ant_dist:.4f} < Cosine_Distance('{base}', '{synonym}') = {syn_dist:.4f}")
        
        print(f"\nScientific Rationale:")
        print(f"The antonym '{antonym}' is vectorially closer to the base word '{base}' than the synonym '{synonym}' is.")
        print("This occurs because word embeddings are generated from co-occurrence statistics in large text corpora.")
        print(f"Words like '{base}' and '{antonym}' (e.g., 'hot' and 'cold', 'happy' and 'sad') frequently appear in nearly identical contexts—they are used to describe the same attributes or states, just at opposite ends of a spectrum.")
        print("For example, one might say 'The weather is hot' or 'The weather is cold'. The surrounding words ('weather', 'is') are the same.")
        print(f"A synonym like '{synonym}' might be used in slightly different, perhaps more nuanced or intense, contexts, causing it to have a different contextual signature and thus be 'further away' in the high-dimensional vector space.")

    else:
        print("\nNo clear cosine distance anomalies were found in the provided test set.")
        print("This suggests that for this specific set of words, the contextual distributions of synonyms were more similar")
        print("to the base words than the antonyms were. A larger, more diverse test set could yield different results.")

except Exception as e:
    print(f"An error occurred during model loading or analysis: {e}")
    print("Please ensure you have a stable internet connection for the initial model download.")

# ------------------

# Run this cell to answer the analogy -- man : grandfather :: woman : x
pprint.pprint(wv_from_bin.most_similar(positive=['woman', 'grandfather'], negative=['man']))




pprint.pprint(wv_from_bin.most_similar(positive=['foot', 'glove'], negative=['hand']))

# For example: x, y, a, b = ("", "", "", "")
# ------------------
# Write your implementation here.
import gensim.downloader as api

def find_financial_analogies(model):
 
    # Format: (word1, word2, word3, expected_word4) for the analogy "word1 is to word2 as word3 is to word4"
    financial_analogy_tests = [
        
        ("usa", "dollar", "japan", "yen"),
        ("china", "yuan", "uk", "pound"),
        ("germany", "euro", "switzerland", "franc"),
        
        
        ("apple", "jobs", "microsoft", "gates"),
        ("amazon", "bezos", "tesla", "musk"),

        
        ("google", "technology", "boeing", "aerospace"),
        ("exxon", "energy", "pfizer", "pharmaceutical"),

      
        ("inflation", "interest", "risk", "return"),
        ("revenue", "profit", "assets", "equity"),

        ("microsoft", "windows", "google", "search"),
        ("toyota", "camry", "ford", "mustang"),
    ]
    
    print("Searching for valid financial analogies...")
    print("-" * 40)
    
    successful_analogies = []
    
    for w1, w2, w3, expected_w4 in financial_analogy_tests:
        try:
            # The core logic: find the word that completes the analogy w1:w2 :: w3:?
            predicted_results = model.most_similar(positive=[w3, w2], negative=[w1], topn=1)
            predicted_w4 = predicted_results[0][0]
       
            status = "✓ SUCCESS" if predicted_w4 == expected_w4 else "✗ FAILED"
            print(f"'{w1}' is to '{w2}' as '{w3}' is to... '{predicted_w4}'? | Expected: '{expected_w4}' | {status}")

            if predicted_w4 == expected_w4:
                successful_analogies.append((w1, w2, w3, expected_w4))

        except KeyError as e:
    
            print(f"INFO: Skipping analogy due to missing word: {e}")
            continue
            
    return successful_analogies



print("Loading pre-trained GloVe model...")
try:
    # We use a smaller model here for faster demonstration. 
    glove_model = api.load("glove-wiki-gigaword-100") 
    print("Model loaded successfully.")

    found_analogies = find_financial_analogies(glove_model)

    print(f"\n{'='*40}")
    print("ANALOGY SEARCH COMPLETE")
    print(f"{'='*40}")

    if found_analogies:
        print(f"Found {len(found_analogies)} successful analogies.")
        w1, w2, w3, w4 = found_analogies[0]
        print(f"\nPrime Example: {w1}:{w2} :: {w3}:{w4}")
        print("This shows the model correctly mapped the relationship between a country and its currency.")
    else:
        print("\nNo perfect analogies were found in this test set.")
        print("This can happen if the relationships are too abstract or if the specific words")
        print("don't have strong contextual links in the training data.")

except Exception as e:
    print(f"An error occurred: {e}")



# ------------------


# Run this cell
# Here `positive` indicates the list of words to be similar to and `negative` indicates the list of words to be
# most dissimilar from.

pprint.pprint(wv_from_bin.most_similar(positive=['man', 'profession'], negative=['woman']))
print()
pprint.pprint(wv_from_bin.most_similar(positive=['woman', 'profession'], negative=['man']))

# ------------------
# Write your implementation here.


# ------------------