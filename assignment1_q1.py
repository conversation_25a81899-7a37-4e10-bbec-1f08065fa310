#!/usr/bin/env python3

"""
Assignment 1 - Question 1.1: Implement distinct_words
CS/STAT 359 - Natural Language Processing

This module implements the distinct_words function to find all unique words
in a corpus and return them in sorted order.
"""

def distinct_words(corpus):
    """ Determine a list of distinct words for the corpus.
        Params:
            corpus (list of list of strings): corpus of documents
        Return:
            corpus_words (list of strings): sorted list of distinct words across the corpus
            n_corpus_words (integer): number of distinct words across the corpus
    """
    corpus_words = []
    n_corpus_words = -1
    
    # ------------------
    # Write your implementation here.
    
    # Step 1: Flatten the nested list structure using list comprehension
    # This takes each document in corpus, then each word in that document
    # and creates a single flat list of all words
    all_words = [word for document in corpus for word in document]
    
    # Step 2: Remove duplicates by converting to set, then back to list
    # Sets automatically handle uniqueness for us
    unique_words = list(set(all_words))
    
    # Step 3: Sort the words alphabetically for consistent ordering
    # This ensures our word indices will be deterministic
    corpus_words = sorted(unique_words)
    
    # Step 4: Count the number of distinct words
    n_corpus_words = len(corpus_words)
    
    # ------------------

    return corpus_words, n_corpus_words


def test_distinct_words():
    """Test function to verify the distinct_words implementation works correctly."""
    
    # Test case 1: Simple example
    test_corpus_1 = [
        ["hello", "world"],
        ["hello", "python", "world"],
        ["python", "is", "great"]
    ]
    
    words_1, count_1 = distinct_words(test_corpus_1)
    print("Test 1:")
    print(f"Corpus: {test_corpus_1}")
    print(f"Distinct words: {words_1}")
    print(f"Count: {count_1}")
    print()
    
    # Test case 2: Empty corpus
    test_corpus_2 = []
    words_2, count_2 = distinct_words(test_corpus_2)
    print("Test 2 (Empty corpus):")
    print(f"Corpus: {test_corpus_2}")
    print(f"Distinct words: {words_2}")
    print(f"Count: {count_2}")
    print()
    
    # Test case 3: Single document
    test_corpus_3 = [["the", "quick", "brown", "fox", "jumps", "over", "the", "lazy", "dog"]]
    words_3, count_3 = distinct_words(test_corpus_3)
    print("Test 3 (Single document with duplicates):")
    print(f"Corpus: {test_corpus_3}")
    print(f"Distinct words: {words_3}")
    print(f"Count: {count_3}")
    print()
    
    # Test case 4: Documents with no overlapping words
    test_corpus_4 = [
        ["cat", "dog"],
        ["bird", "fish"],
        ["elephant", "tiger"]
    ]
    words_4, count_4 = distinct_words(test_corpus_4)
    print("Test 4 (No overlapping words):")
    print(f"Corpus: {test_corpus_4}")
    print(f"Distinct words: {words_4}")
    print(f"Count: {count_4}")


if __name__ == "__main__":
    print("Testing distinct_words function...")
    print("=" * 50)
    test_distinct_words()
    print("=" * 50)
    print("All tests completed!")
